import { getCookie } from "@/actions/cookies";
import { apiServer } from "@/lib/apiServer";
import CONSTANT from "@/lib/constant";
import { redirect } from "next/navigation";

export type ServerAllowedRole = "USER" | "PROVIDER" | "ADMIN";

interface ServerRoleGuardOptions {
  allowedRoles: ServerAllowedRole[];
  fallbackPath?: string;
  roomId?: string; // For context-based role detection
}

interface UserWithRole {
  user_id: string;
  first_name: string;
  last_name: string;
  email?: string;
  role?: string;
  user_type?: string;
}

/**
 * Server-side role-based access control utility
 * Validates user roles on the server and redirects if unauthorized
 */
export async function validateServerRole({
  allowedRoles,
  fallbackPath = "/",
  roomId,
}: ServerRoleGuardOptions): Promise<{
  isAuthorized: boolean;
  token: string | null;
  user: UserWithRole | null;
  userRole: string | null;
}> {
  try {
    // Get token from cookies
    const token = await getCookie(CONSTANT.ACCESS_TOKEN);
    if (!token) {
      redirect(fallbackPath);
    }

    // Get user data from cookies
    const userStr = await getCookie("user");
    let user: UserWithRole | null = null;

    if (userStr) {
      try {
        user = JSON.parse(userStr);
      } catch (error) {
        console.error("Failed to parse user data:", error);
      }
    }

    // Determine user role
    let detectedRole: string | null = null;

    if (user?.role) {
      detectedRole = user.role.toUpperCase();
    } else if (user?.user_type) {
      detectedRole = user.user_type.toUpperCase();
    } else {
      // Try to get role from API
      try {
        const response = await apiServer({
          method: "GET",
          endpoint: "auth/user/profile",
          token,
        });

        if (response.statusCode === 200 && response.data) {
          detectedRole =
            response.data.role?.toUpperCase() ||
            response.data.user_type?.toUpperCase();
        }
      } catch (error) {
        console.warn("Failed to fetch user role from API:", error);
      }
    }

    // If still no role detected and we have roomId, try to infer from room metadata
    if (!detectedRole && roomId) {
      try {
        const roomResponse = await apiServer({
          method: "GET",
          endpoint: `chat/${roomId}/metadata`,
          token,
        });

        if (roomResponse.statusCode === 200 && roomResponse.data) {
          // Check if current user is the patient or provider in this room
          const { patient, provider } = roomResponse.data;
          if (user && patient?.user_id === user.user_id) {
            detectedRole = "USER";
          } else if (user && provider?.user_id === user.user_id) {
            detectedRole = "PROVIDER";
          }
        }
      } catch (error) {
        console.warn("Failed to get room metadata for role detection:", error);
      }
    }

    // Check if user role is in allowed roles
    const isAuthorized = detectedRole
      ? allowedRoles.includes(detectedRole as ServerAllowedRole)
      : false;

    if (!isAuthorized) {
      redirect(fallbackPath);
    }

    return {
      isAuthorized: true,
      token,
      user,
      userRole: detectedRole,
    };
  } catch (error) {
    console.error("Server role validation failed:", error);
    redirect(fallbackPath);
  }
}

/**
 * Higher-order function to wrap server components with role-based access control
 */
export function withServerRoleGuard<T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  options: ServerRoleGuardOptions,
) {
  return async function ProtectedComponent(props: T) {
    await validateServerRole(options);
    return <Component {...props} />;
  };
}
