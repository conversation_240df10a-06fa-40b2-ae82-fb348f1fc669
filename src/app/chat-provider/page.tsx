import ProviderChatUIComponent from "@/components/chat-ui/provider-chat";
import { validateServerRole } from "@/lib/server-role-guard";
import { cookies } from "next/headers";
import React from "react";
export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

const ProvideChatUIPage = async () => {
  // Validate user role - only PROVIDER role allowed for provider chat
  await validateServerRole({
    allowedRoles: ["PROVIDER"],
    fallbackPath: "/",
  });

  const cookieStore = cookies();
  const token = cookieStore.get("accessToken")!;
  return (
    <>
      <ProviderChatUIComponent token={token?.value} />
    </>
  );
};

export default ProvideChatUIPage;
