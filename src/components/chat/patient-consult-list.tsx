"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { apiClient } from "@/lib/api";
import { tokenManager } from "@/lib/tokenManager";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Loader } from "@/components/shared/loader";
import { OtpModal } from "@/components/shared/otp-modal";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, User, MessageCircle } from "lucide-react";

interface Consult {
  id: string;
  order_guid: string;
  patient_name: string;
  provider_name: string;
  status: string;
  created_at: string;
  room_id?: string;
  description?: string;
}

interface ApiResponse<T = any> {
  statusCode: number;
  message: string;
  data?: T;
}

/**
 * Patient Consult List Component
 * Handles authentication and displays list of patient consultations
 */
const PatientConsultListComponent: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [email, setEmail] = useState("");
  const [role] = useState("USER"); // Default role as specified
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [consults, setConsults] = useState<Consult[]>([]);
  const [isLoadingConsults, setIsLoadingConsults] = useState(false);
  const [userInfo, setUserInfo] = useState<any>(null);

  const router = useRouter();

  // Check authentication status on component mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    setIsLoading(true);
    try {
      const token = await tokenManager.getTokenWithMigration();
      if (token) {
        setIsAuthenticated(true);
        await loadConsults(token);
      } else {
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error("Failed to check auth status:", error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const sendOtp = async () => {
    if (!email.trim()) {
      toast.error("Please enter your email address");
      return;
    }

    setIsLoading(true);
    try {
      const response: ApiResponse = await apiClient({
        method: "POST",
        endpoint: "auth/send-otp-role-email",
        data: {
          role: role,
          email: email,
        },
      });

      if (response.statusCode === 200) {
        setUserInfo(response.data?.user);
        setShowOtpModal(true);
        toast.success(response.message || "OTP sent successfully");
      } else {
        toast.error(response.message || "Failed to send OTP");
      }
    } catch (error) {
      console.error("Failed to send OTP:", error);
      toast.error("Failed to send OTP");
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOtp = async (otp: string): Promise<boolean> => {
    try {
      const response: ApiResponse = await apiClient({
        method: "POST",
        endpoint: "auth/verify-otp-by-email",
        data: {
          email: email,
          otp: otp,
        },
      });

      if (response.statusCode === 200) {
        await tokenManager.setToken(response.data.token, response.data.user);
        setIsAuthenticated(true);
        setShowOtpModal(false);
        await loadConsults(response.data.token);
        toast.success("Successfully authenticated!");
        return true;
      } else {
        toast.error(response.message || "Invalid OTP");
        return false;
      }
    } catch (error) {
      console.error("OTP verification failed:", error);
      toast.error("OTP verification failed");
      return false;
    }
  };

  const loadConsults = async (token: string) => {
    setIsLoadingConsults(true);
    try {
      // This is a placeholder API call - you'll need to implement the actual consult list endpoint
      const response: ApiResponse<Consult[]> = await apiClient({
        method: "GET",
        endpoint: "consults", // This endpoint needs to be implemented
        token,
      });

      if (response.statusCode === 200 && response.data) {
        setConsults(response.data);
      } else {
        // For now, show empty state if API doesn't exist
        setConsults([]);
        console.log("Consult list API not implemented yet");
      }
    } catch (error) {
      console.error("Failed to load consults:", error);
      setConsults([]);
    } finally {
      setIsLoadingConsults(false);
    }
  };

  const handleConsultClick = async (consult: Consult) => {
    if (consult.room_id) {
      router.push(`/chat/patient/${consult.room_id}`);
    } else {
      // Create room using order GUID if room doesn't exist
      try {
        const response = await apiClient({
          method: "POST",
          endpoint: "room/create/by-order-guid",
          data: {
            order_guid: consult.order_guid,
            description: `Consultation room for order ${consult.order_guid}`,
            sms_status: true,
          },
          token: (await tokenManager.getToken()) as string,
        });

        if (response.statusCode === 200 && response.data?.room) {
          router.push(`/chat/patient/${response.data.room.id}`);
        } else {
          toast.error("Failed to create chat room");
        }
      } catch (error) {
        console.error("Failed to create room:", error);
        toast.error("Failed to create chat room");
      }
    }
  };

  const handleResendOtp = async () => {
    await sendOtp();
  };

  // Show loading state
  if (isLoading) {
    return <Loader show={true} />;
  }

  // Show login form if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Patient Portal Login
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Enter your email to access your consultations
            </p>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Email Verification</CardTitle>
              <CardDescription>
                We&apos;ll send you an OTP to verify your identity
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Input
                  id="role"
                  value={role}
                  disabled
                  className="bg-gray-100"
                />
              </div>
              <Button
                onClick={sendOtp}
                disabled={isLoading || !email.trim()}
                className="w-full"
              >
                {isLoading ? "Sending..." : "Send OTP"}
              </Button>
            </CardContent>
          </Card>

          {/* OTP Modal */}
          <OtpModal
            isOpen={showOtpModal}
            onClose={() => setShowOtpModal(false)}
            onVerify={verifyOtp}
            onResendOtp={handleResendOtp}
            userEmail={email}
            title="Enter Verification Code"
            description="Please enter the OTP sent to your email address."
          />
        </div>
      </div>
    );
  }

  // Show consult list for authenticated users
  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">My Consultations</h1>
          <p className="mt-2 text-gray-600">
            View and access your medical consultations
          </p>
        </div>

        {isLoadingConsults ? (
          <Loader show={true} />
        ) : consults.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <MessageCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No consultations found
              </h3>
              <p className="text-gray-600">
                You don&apos;t have any consultations yet. When you have
                scheduled consultations, they will appear here.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {consults.map((consult) => (
              <Card
                key={consult.id}
                className="cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => handleConsultClick(consult)}
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">
                      {consult.patient_name}
                    </CardTitle>
                    <Badge
                      variant={
                        consult.status === "active" ? "default" : "secondary"
                      }
                    >
                      {consult.status}
                    </Badge>
                  </div>
                  <CardDescription>{consult.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <User className="h-4 w-4 mr-2" />
                      Dr. {consult.provider_name}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      {new Date(consult.created_at).toLocaleDateString()}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Clock className="h-4 w-4 mr-2" />
                      {new Date(consult.created_at).toLocaleTimeString()}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PatientConsultListComponent;
